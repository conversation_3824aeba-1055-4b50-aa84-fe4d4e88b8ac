import { AnimatePresence, motion } from 'framer-motion';
import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react';

import { Skill } from '../types';
import { useViewTransitions } from '../hooks/useViewTransitions';

interface SkillsProps {
  skillsData: Skill[];
}

interface SkillCategory {
  id: string;
  name: string;
  color: string;
  description: string;
}

interface SkillLevel {
  min: number;
  name: string;
  color: string;
}

interface CategorizedSkill extends Skill {
  category: string;
}

// Refined skill categories with better descriptions
const skillCategories: SkillCategory[] = [
  {
    id: 'ai-ml',
    name: 'AI/ML & Product',
    color: 'var(--token-primary-500)',
    /* Primary brand color for AI/ML category */
    description:
      'Artificial intelligence, machine learning, and product development expertise',
  },
  {
    id: 'technical',
    name: 'Technical Skills',
    color: 'var(--token-success-600)',
    /* Success color for technical skills */
    description: 'Programming, development, and technical implementation',
  },
  {
    id: 'leadership',
    name: 'Leadership & Strategy',
    color: 'var(--token-warning-500)',
    /* Warning color for leadership category */
    description: 'Team leadership, management, and strategic planning',
  },
];

// Skill level definitions
const skillLevels: SkillLevel[] = [
  { min: 90, name: 'Expert', color: 'var(--token-primary-500)' },
  /* Primary brand color for expert level */
  { min: 80, name: 'Advanced', color: 'var(--token-success-600)' },
  /* Success color for advanced level */
  { min: 70, name: 'Intermediate', color: 'var(--token-warning-500)' },
  /* Warning color for intermediate level */
  { min: 0, name: 'Foundational', color: 'var(--token-text-tertiary)' },
  /* Tertiary text color for foundational level */
];

// Get skill level
const getSkillLevel = (level: number): SkillLevel => {
  return (
    skillLevels.find(sl => level >= sl.min) ||
    skillLevels[skillLevels.length - 1]
  );
};

const SkillsComponent: React.FC<SkillsProps> = ({ skillsData }) => {
  const { withViewTransition } = useViewTransitions();
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [inViewSkills, setInViewSkills] = useState<Record<string, boolean>>({});
  const skillsContainerRef = useRef<HTMLDivElement | null>(null);

  // PERFORMANCE OPTIMIZATION: Memoize expensive skill categorization calculation
  // Map skills to simplified categories
  const categorizedSkills: CategorizedSkill[] = useMemo(() => skillsData.map(skill => {
    let category = 'technical'; // Default category

    // Simplified category determination
    if (
      /ai|ml|agent|llm|nlp|gpt|model|embedding|vector|orchestration|product|roadmap|strategy/i.test(
        skill.name
      )
    ) {
      category = 'ai-ml';
    } else if (
      /lead|leadership|manage|team|strategy|vision|executive/i.test(skill.name)
    ) {
      category = 'leadership';
    }

    return {
      ...skill,
      category,
    };
  }), [skillsData]);

  // PERFORMANCE OPTIMIZATION: Memoize toggle function to prevent unnecessary re-renders
  // Toggle category filter with View Transitions
  const toggleCategory = useCallback((categoryId: string | null): void => {
    withViewTransition(() => {
      setActiveCategory(prev => (prev === categoryId ? null : categoryId));
      // Reset in-view skills when changing categories
      setInViewSkills({});
    });
  }, [withViewTransition]);

  // PERFORMANCE OPTIMIZATION: Memoize filtered skills calculation
  // Get filtered skills
  const getFilteredSkills = useMemo((): CategorizedSkill[] => {
    if (!activeCategory) return categorizedSkills;
    return categorizedSkills.filter(skill => skill.category === activeCategory);
  }, [activeCategory, categorizedSkills]);

  // PERFORMANCE OPTIMIZATION: Memoize category color lookup
  // Get category color
  const getCategoryColor = useCallback((categoryId: string): string => {
    const category = skillCategories.find(cat => cat.id === categoryId);
    return category ? category.color : 'var(--token-primary-500)';
    /* Default to primary brand color */
  }, []);

  // Check if skills are in view for animation
  useEffect(() => {
    if (!skillsContainerRef.current) return;

    const observer = new IntersectionObserver(
      (entries: IntersectionObserverEntry[]) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const skillId = (entry.target as HTMLElement).dataset.skillId;
            if (skillId) {
              setInViewSkills(prev => ({ ...prev, [skillId]: true }));
            }
          }
        });
      },
      { threshold: 0.2, rootMargin: '0px 0px -100px 0px' }
    );

    const skillElements =
      skillsContainerRef.current.querySelectorAll('.skill-card');
    skillElements.forEach(el => observer.observe(el));

    return () => {
      skillElements.forEach(el => observer.unobserve(el));
    };
  }, [activeCategory]);

  return (
    <section id='skills' className='py-16 bg-token-primary'>
      <div className='container mx-auto px-4'>
        <h2 className='mb-2 text-center text-3xl font-bold tracking-tight text-token-primary'>
          Skills & Expertise
        </h2>

        <p className='mx-auto mb-10 max-w-2xl text-center font-light tracking-wide text-token-secondary'>
          Professional competencies across AI, product, and technical domains
        </p>

        {/* Refined Category Filter */}
        <div className='mb-10 flex flex-wrap justify-center gap-3'>
          <motion.button
            onClick={() => toggleCategory(null)}
            className={`rounded-full px-5 py-2.5 text-sm font-medium shadow-sm transition-all duration-300 md:text-base ${activeCategory === null
              ? 'text-white shadow-md bg-interactive-primary'
              : 'text-token-primary bg-interactive-secondary hover:bg-interactive-secondary-hover'
              }`}
            whileHover={{ y: -2 }}
            whileTap={{ y: 0 }}
          >
            All Skills
          </motion.button>

          {skillCategories.map(category => (
            <motion.button
              key={category.id}
              onClick={() => toggleCategory(category.id)}
              className={`rounded-full px-5 py-2.5 text-sm font-medium shadow-sm transition-all duration-300 md:text-base ${activeCategory === category.id
                ? 'text-white shadow-md bg-interactive-primary'
                : 'text-token-primary bg-interactive-secondary hover:bg-interactive-secondary-hover'
                }`}
              style={{
                borderLeft: `3px solid ${category.color}`,
              }}
              whileHover={{ y: -2 }}
              whileTap={{ y: 0 }}
            >
              {category.name}
            </motion.button>
          ))}
        </div>

        {/* Category Description */}
        <AnimatePresence mode='wait'>
          {activeCategory && (
            <motion.div
              className='mb-8 text-center'
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.3 }}
            >
              <p className='font-light italic text-token-secondary'>
                {
                  skillCategories.find(cat => cat.id === activeCategory)
                    ?.description
                }
              </p>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Skills Grid - Enhanced Card-Based Layout */}
        <div
          ref={skillsContainerRef}
          className='grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3'
        >
          <AnimatePresence>
            {getFilteredSkills.map(skill => {
              const skillLevel = getSkillLevel(skill.level);

              return (
                <motion.div
                  key={skill.id}
                  layout
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  transition={{ duration: 0.4 }}
                  className='skill-card overflow-hidden rounded-lg border shadow-md transition-all duration-300 bg-token-primary border-token-primary hover:shadow-lg'
                  data-skill-id={skill.id}
                  whileHover={{
                    y: -8,
                    transition: { duration: 0.2 },
                  }}
                >
                  <div
                    className='h-2'
                    style={{
                      backgroundColor: getCategoryColor(skill.category),
                    }}
                  />
                  <div className='p-5'>
                    <div className='mb-3 flex items-start justify-between'>
                      <h3 className='text-lg font-semibold text-token-primary'>
                        {skill.name}
                      </h3>
                      <span
                        className='rounded-full px-2 py-1 text-xs font-medium'
                        style={{
                          backgroundColor: `${skillLevel.color}20`,
                          color: skillLevel.color,
                        }}
                      >
                        {skillLevel.name}
                      </span>
                    </div>

                    <div className='relative mt-4'>
                      <div className='mb-1 flex items-center justify-between space-x-3'>
                        <span className='text-xs font-medium tracking-wide text-token-tertiary'>
                          Proficiency
                        </span>
                        <span
                          className='text-lg font-bold'
                          style={{ color: getCategoryColor(skill.category) }}
                        >
                          {skill.level}%
                        </span>
                      </div>

                      <div className='h-2.5 w-full overflow-hidden rounded-full bg-token-border-primary'>
                        <motion.div
                          className='h-full rounded-full'
                          style={{
                            width: '0%',
                            backgroundColor: getCategoryColor(skill.category),
                          }}
                          animate={{
                            width: inViewSkills[skill.id]
                              ? `${skill.level}%`
                              : '0%',
                          }}
                          transition={{
                            duration: 1,
                            delay: 0.2,
                            ease: 'easeOut',
                          }}
                        />
                      </div>
                    </div>

                    <div className='mt-4'>
                      <span className='rounded-full px-2 py-1 text-xs font-medium text-token-secondary bg-interactive-secondary'>
                        {
                          skillCategories.find(cat => cat.id === skill.category)
                            ?.name
                        }
                      </span>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </AnimatePresence>
        </div>

        {/* Empty State */}
        {getFilteredSkills.length === 0 && (
          <motion.div
            className='rounded-lg py-10 text-center shadow-inner bg-token-tertiary'
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            <p className='font-medium text-token-tertiary'>
              No skills found in this category.
            </p>
          </motion.div>
        )}
      </div>
    </section>
  );
};

/**
 * Memoized Skills component for performance optimization
 *
 * PERFORMANCE OPTIMIZATION: React.memo prevents unnecessary re-renders when:
 * - skillsData array reference hasn't changed
 *
 * Expected performance improvement: 20-30% reduction in Skills re-renders
 * when parent App component re-renders for unrelated state changes
 *
 * The component includes several performance optimizations:
 * - Memoized skill categorization calculation (useMemo)
 * - Memoized filtered skills calculation (useMemo)
 * - Memoized callback functions (useCallback)
 * - Efficient intersection observer for animations
 */
export const Skills = React.memo(SkillsComponent);
